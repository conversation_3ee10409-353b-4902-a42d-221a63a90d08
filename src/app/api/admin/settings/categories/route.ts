import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/admin/settings/categories - Get all categories with their metadata
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get all category info settings
    const categoryInfoSettings = await prisma.sitesettings.findMany({
      where: {
        key: {
          endsWith: '_CATEGORY_INFO'
        },
        fieldtype: 'json'
      },
      orderBy: {
        category: 'asc'
      }
    })

    // Parse category data and include order information
    const categories = categoryInfoSettings.map((setting, index) => {
      try {
        const categoryData = JSON.parse(setting.value)
        return {
          name: setting.category,
          label: categoryData.label || setting.category,
          description: categoryData.description || `Settings for ${categoryData.label || setting.category}`,
          color: categoryData.color || 'gray',
          icon: categoryData.icon || 'DocumentTextIcon',
          order: categoryData.order || index
        }
      } catch (error) {
        // Fallback for invalid JSON
        return {
          name: setting.category,
          label: setting.category,
          description: `Settings for ${setting.category}`,
          color: 'gray',
          icon: 'DocumentTextIcon',
          order: index
        }
      }
    })

    // Also get categories that don't have info settings (legacy categories)
    const allCategories = await prisma.sitesettings.findMany({
      select: { category: true },
      distinct: ['category'],
      where: {
        NOT: {
          key: { endsWith: '_CATEGORY_INFO' }
        }
      }
    })

    // Add legacy categories that don't have info settings
    const existingCategoryNames = categories.map(c => c.name)
    const legacyCategories = allCategories
      .filter(c => !existingCategoryNames.includes(c.category))
      .map((c, index) => ({
        name: c.category,
        label: c.category.charAt(0) + c.category.slice(1).toLowerCase(),
        description: `Settings for ${c.category}`,
        color: 'gray',
        icon: 'DocumentTextIcon',
        order: categories.length + index
      }))

    const allCategoriesWithInfo = [...categories, ...legacyCategories]
      .sort((a, b) => a.order - b.order)

    return NextResponse.json({
      success: true,
      data: allCategoriesWithInfo,
      message: 'Categories retrieved successfully'
    })

  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/settings/categories - Create a new category (by creating a placeholder setting)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    if (!body.name || !body.label) {
      return NextResponse.json(
        { success: false, error: 'Category name and label are required' },
        { status: 400 }
      )
    }

    const categoryName = body.name.toUpperCase().replace(/\s+/g, '_')

    // Check if category already exists
    const existingCategory = await prisma.sitesettings.findFirst({
      where: { category: categoryName }
    })

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Category already exists' },
        { status: 400 }
      )
    }

    // Build style JSON
    const styleJson = {
      label: body.label,
      description: body.description || `Settings for ${body.label}`,
      color: body.color || 'gray',
      icon: body.icon || 'DocumentTextIcon',
      order: body.order || 0
    }

    // Create a placeholder setting for the new category
    const placeholderSetting = await prisma.sitesettings.create({
      data: {
        key: `${categoryName}_CATEGORY_INFO`,
        value: JSON.stringify(styleJson),
        category: categoryName,
        fieldtype: 'json',
        description: `Category information for ${body.label}`,
        ispublic: false,
        isactive: true,
        categorystyle: styleJson
      } as any
    })

    // Propagate style to all settings in this category (should be none on creation)

    return NextResponse.json({
      success: true,
      data: {
        name: categoryName,
        label: body.label,
        description: body.description,
        color: body.color,
        icon: body.icon,
        order: body.order,
        categorystyle: styleJson
      },
      message: 'Category created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/settings/categories - Update an existing category
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    if (!body.name || !body.label) {
      return NextResponse.json(
        { success: false, error: 'Category name and label are required' },
        { status: 400 }
      )
    }

    const categoryName = body.name.toUpperCase().replace(/\s+/g, '_')

    // Build style JSON
    const styleJson = {
      label: body.label,
      description: body.description || `Settings for ${body.label}`,
      color: body.color || 'gray',
      icon: body.icon || 'DocumentTextIcon',
      order: body.order || 0
    }

    // Only update the categorystyle for all settings in this category
    await prisma.sitesettings.updateMany({
      where: { category: categoryName },
      data: { categorystyle: styleJson } as any
    })

    // Fetch updated categories to return to the frontend
    const updatedCategories = await prisma.sitesettings.findMany({
      where: { category: categoryName },
      select: {
        category: true,
        categorystyle: true
      } as any
    })

    return NextResponse.json({
      success: true,
      data: {
        name: categoryName,
        label: body.label,
        description: body.description,
        color: body.color,
        icon: body.icon,
        order: body.order,
        categorystyle: styleJson
      },
      message: 'Category style updated for all settings in this category',
      updatedCategories
    })

  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
